"""Unit tests for essential processor functionality."""
import base64
import json
import pytest
from typing import Any, Dict, List, Optional
from unittest.mock import Mock

from llm_grok.processors import ImageProcessor, ToolProcessor, StreamProcessor
from llm_grok.processors import ProcessingError, ValidationError


class TestImageProcessor:
    """Test basic image processing functionality."""
    
    @pytest.fixture
    def processor(self) -> ImageProcessor:
        """Create an ImageProcessor instance."""
        return ImageProcessor("x-ai/grok-4")
    
    def test_validate_image_format_url(self, processor) -> None:
        """Test validation of image URLs."""
        url = "https://example.com/image.jpg"
        result = processor.validate_image_format(url)
        assert result == url
    
    def test_validate_image_format_data_url(self, processor) -> None:
        """Test validation of data URLs."""
        data_url = "data:image/jpeg;base64,/9j/4AAQ..."
        result = processor.validate_image_format(data_url)
        assert result == data_url
    
    def test_validate_image_format_base64(self, processor) -> None:
        """Test validation of base64 data."""
        # Valid base64 for a tiny 1x1 transparent PNG
        valid_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAAAAAA6fptVAAAACklEQVQI12P4DwABAQEAWk1v8QAAAABJRU5ErkJggg=="
        result = processor.validate_image_format(valid_base64)
        # Should convert to data URL
        assert result.startswith("data:image/")
        assert valid_base64 in result
    
    def test_validate_image_format_invalid(self, processor) -> None:
        """Test validation of invalid image data."""
        with pytest.raises(ValueError, match="Invalid image"):
            processor.validate_image_format("not-valid-image-data")
    
    def test_build_multimodal_content_with_url(self, processor) -> None:
        """Test building multimodal content with URL attachment."""
        # Mock attachment
        attachment = Mock()
        attachment.type = "image"
        attachment.url = "https://example.com/image.jpg"
        attachment.content = None
        attachment.resolve_type.return_value = "image/jpeg"
        
        result = processor.build_multimodal_content("Test prompt", [attachment])
        
        assert len(result) == 2
        assert result[0] == {"type": "text", "text": "Test prompt"}
        assert result[1]["type"] == "image_url"
        assert result[1]["image_url"]["url"] == "https://example.com/image.jpg"


class TestToolProcessor:
    """Test basic tool processing functionality."""
    
    @pytest.fixture
    def processor(self) -> ToolProcessor:
        """Create a ToolProcessor instance."""
        return ToolProcessor("x-ai/grok-4")
    
    def test_accumulate_tool_call_single(self, processor) -> None:
        """Test accumulating a single tool call."""
        tool_calls: List[Dict[str, Any]] = []
        
        delta = {
            "tool_calls": [{
                "index": 0,
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "NYC"}'
                }
            }]
        }
        
        processor.accumulate_tool_call(tool_calls, delta)
        
        assert len(tool_calls) == 1
        assert tool_calls[0]["id"] == "call_123"
        assert tool_calls[0]["function"]["name"] == "get_weather"
        assert tool_calls[0]["function"]["arguments"] == '{"location": "NYC"}'
    
    def test_finalize_tool_calls_valid(self, processor) -> None:
        """Test finalizing valid tool calls."""
        tool_calls = [{
            "id": "call_123",
            "type": "function",
            "function": {
                "name": "get_weather",
                "arguments": '{"location": "NYC"}'
            }
        }]
        
        # Mock LLM response
        response = Mock()
        
        finalized = processor.finalize_tool_calls(tool_calls, response)
        
        assert len(finalized) == 1
        assert finalized[0]["id"] == "call_123"
        assert finalized[0]["function"]["name"] == "get_weather"
        
        # Check that tool calls were set on response
        assert hasattr(response, '_tool_calls')
        assert response._tool_calls == finalized
    
    def test_validate_tool_definition_valid(self, processor) -> None:
        """Test validation of valid tool definition."""
        tool = {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string"}
                    },
                    "required": ["location"]
                }
            }
        }
        
        # Should not raise
        processor.validate_tool_definition(tool)
    
    def test_validate_tool_definition_invalid(self, processor) -> None:
        """Test validation of invalid tool definition."""
        # Missing function name
        tool = {
            "type": "function",
            "function": {
                "description": "Get weather"
            }
        }
        
        with pytest.raises(ValidationError):
            processor.validate_tool_definition(tool)


class TestStreamProcessor:
    """Test basic streaming functionality."""
    
    @pytest.fixture
    def processor(self) -> StreamProcessor:
        """Create a StreamProcessor instance."""
        return StreamProcessor("x-ai/grok-4")
    
    def test_process_stream_delta_content(self, processor) -> None:
        """Test processing stream delta with content."""
        delta = {"content": "Hello world"}
        tool_calls: List[Dict[str, Any]] = []
        
        content = processor.process_stream_delta(delta, tool_calls)
        
        assert content == "Hello world"
        assert len(tool_calls) == 0
    
    def test_process_stream_delta_tool_calls(self, processor) -> None:
        """Test processing stream delta with tool calls."""
        delta = {
            "tool_calls": [{
                "index": 0,
                "id": "call_123",
                "function": {"name": "get_weather"}
            }]
        }
        tool_calls: List[Dict[str, Any]] = []
        
        content = processor.process_stream_delta(delta, tool_calls)
        
        assert content is None
        assert len(tool_calls) == 1
        assert tool_calls[0]["id"] == "call_123"
    
    def test_process_stream_openai(self, processor) -> None:
        """Test processing OpenAI format stream event."""
        event = {
            "choices": [{
                "delta": {"content": "Test response"},
                "index": 0
            }]
        }
        tool_calls: List[Dict[str, Any]] = []
        
        chunks = list(processor.process_stream(event, tool_calls, format="openai"))
        
        assert chunks == ["Test response"]
    
    def test_process_stream_anthropic(self, processor) -> None:
        """Test processing Anthropic format stream event."""
        event = {
            "type": "content_block_delta",
            "delta": {"text": "Test response"}
        }
        tool_calls: List[Dict[str, Any]] = []
        
        chunks = list(processor.process_stream(event, tool_calls, format="anthropic"))
        
        assert chunks == ["Test response"]


class TestProcessingExceptions:
    """Test exception handling."""
    
    def test_processing_error(self) -> None:
        """Test ProcessingError creation."""
        error = ProcessingError("Test error", details={"key": "value"})
        assert str(error) == "Test error"
        assert error.details == {"key": "value"}
    
    def test_validation_error(self) -> None:
        """Test ValidationError creation."""
        error = ValidationError("Invalid input", field="location")
        assert str(error) == "Invalid input"
        assert error.field == "location"
        assert error.error_code == "validation_error"