"""Unit tests for format handlers."""

import json
from typing import List

import pytest

from llm_grok.formats import OpenAIFormatHandler, AnthropicFormatHandler
from llm_grok.types import Message, ToolDefinition


class TestOpenAIFormatHandler:
    """Test OpenAI format handler functionality."""
    
    def test_convert_simple_messages_to_anthropic(self) -> None:
        """Test converting simple OpenAI messages to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {"role": "system", "content": "You are helpful."},
            {"role": "user", "content": "Hello!"},
            {"role": "assistant", "content": "Hi there!"},
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        assert result.get("system") == "You are helpful."
        messages_list = result.get("messages", [])
        assert len(messages_list) == 2
        assert messages_list[0]["role"] == "user"
        assert messages_list[0]["content"] == "Hello!"
        assert messages_list[1]["role"] == "assistant"
        assert messages_list[1]["content"] == "Hi there!"
    
    def test_convert_multimodal_messages_to_anthropic(self) -> None:
        """Test converting multimodal OpenAI messages to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "What's in this image?"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        messages_list = result.get("messages", [])
        assert len(messages_list) == 1
        content = messages_list[0]["content"]
        assert isinstance(content, list)
        assert len(content) == 2
        assert content[0]["type"] == "text"
        assert content[1]["type"] == "image"
    
    def test_convert_tools_to_anthropic(self) -> None:
        """Test converting OpenAI tools to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        tools: List[ToolDefinition] = [{
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string"}
                    },
                    "required": ["location"]
                }
            }
        }]
        
        result = handler.convert_tools_to_anthropic(tools)
        
        assert len(result) == 1
        assert result[0]["name"] == "get_weather"
        assert result[0]["description"] == "Get weather for a location"
        assert result[0]["input_schema"] == tools[0]["function"]["parameters"]
    
    def test_convert_from_anthropic_response(self) -> None:
        """Test converting Anthropic response to OpenAI format."""
        handler = OpenAIFormatHandler("grok-4")
        
        anthropic_response = {
            "id": "msg_123",
            "type": "message",
            "role": "assistant",
            "content": [{"type": "text", "text": "Hello!"}],
            "model": "grok-4",
            "stop_reason": "end_turn"
        }
        
        result = handler.convert_from_anthropic_response(anthropic_response)
        
        assert result["id"] == "msg_123"
        assert result["model"] == "grok-4"
        assert result["choices"][0]["message"]["role"] == "assistant"
        assert result["choices"][0]["message"]["content"] == "Hello!"
        assert result["choices"][0]["finish_reason"] == "stop"
    
    def test_parse_openai_sse(self) -> None:
        """Test parsing OpenAI SSE format."""
        handler = OpenAIFormatHandler("grok-4")
        
        # Valid SSE event
        event = handler.parse_sse('data: {"id":"123","choices":[{"delta":{"content":"Hi"}}]}')
        assert event is not None
        assert event["id"] == "123"
        
        # [DONE] event
        event = handler.parse_sse("data: [DONE]")
        assert event is None
        
        # Invalid JSON
        event = handler.parse_sse("data: invalid json")
        assert event is None


class TestAnthropicFormatHandler:
    """Test Anthropic format handler functionality."""
    
    def test_parse_anthropic_sse(self) -> None:
        """Test parsing Anthropic SSE format."""
        handler = AnthropicFormatHandler("grok-4")
        
        # Valid event
        event = handler.parse_sse('data: {"type":"content_block_delta","delta":{"text":"Hi"}}')
        assert event is not None
        assert event["type"] == "content_block_delta"
        
        # Invalid JSON
        event = handler.parse_sse("data: invalid")
        assert event is None
    
    def test_convert_from_anthropic_response(self) -> None:
        """Test converting Anthropic response (pass-through)."""
        handler = AnthropicFormatHandler("grok-4")
        
        response = {
            "id": "msg_123",
            "content": [{"type": "text", "text": "Hello"}]
        }
        
        # Should pass through unchanged
        result = handler.convert_from_anthropic_response(response)
        assert result == response