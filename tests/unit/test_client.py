"""Tests for the HTTP client module."""

import json
import httpx
import pytest
from pytest_httpx import HTTP<PERSON><PERSON>ock

from llm_grok.client import GrokClient
from llm_grok.exceptions import (
    GrokError,
    RateLimitError,
    APIError,
    AuthenticationError,
)
from tests.utils.mocks import (
    TEST_API_KEY,
    CHAT_COMPLETIONS_URL,
    MESSAGES_URL,
    ERROR_RESPONSES,
    create_chat_completion_response,
    create_messages_response,
)


class TestGrokClient:
    """Test cases for GrokClient class."""
    
    def test_client_initialization(self) -> None:
        """Test client initialization."""
        client = GrokClient(api_key=TEST_API_KEY)
        assert client.api_key == TEST_API_KEY
        assert client.timeout == 60.0  # Default timeout
    
    def test_successful_request(self, httpx_mock: HTTPXMock) -> None:
        """Test successful API request."""
        expected_response = create_chat_completion_response("Test response")
        
        httpx_mock.add_response(
            method="POST",
            url=CHAT_COMPLETIONS_URL,
            json=expected_response
        )
        
        client = GrokClient(api_key=TEST_API_KEY)
        body = {
            "model": "x-ai/grok-4",
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False
        }
        
        response = client.post_openai_completion(
            messages=body["messages"],
            model=body["model"],
            stream=body["stream"]
        )
        
        assert response == expected_response
        
        # Verify request
        request = httpx_mock.get_request()
        assert request is not None
        assert request.headers["Authorization"] == f"Bearer {TEST_API_KEY}"
        assert json.loads(request.content) == body
    
    def test_rate_limit_retry(self, httpx_mock: HTTPXMock) -> None:
        """Test rate limit retry mechanism."""
        # First request fails with rate limit
        httpx_mock.add_response(
            method="POST",
            url=CHAT_COMPLETIONS_URL,
            status_code=429,
            json=ERROR_RESPONSES["rate_limit"],
            headers={"Retry-After": "1"}
        )
        
        # Second request succeeds
        expected_response = create_chat_completion_response("Success after retry")
        httpx_mock.add_response(
            method="POST",
            url=CHAT_COMPLETIONS_URL,
            json=expected_response
        )
        
        client = GrokClient(api_key=TEST_API_KEY)
        body = {"model": "x-ai/grok-4", "messages": []}
        
        # Should retry and succeed
        response = client.post_openai_completion(
            messages=body["messages"],
            model=body["model"]
        )
        assert response == expected_response
        
        # Verify two requests were made
        requests = httpx_mock.get_requests()
        assert len(requests) == 2
    
    def test_authentication_error(self, httpx_mock: HTTPXMock) -> None:
        """Test authentication error handling."""
        httpx_mock.add_response(
            method="POST",
            url=CHAT_COMPLETIONS_URL,
            status_code=401,
            json=ERROR_RESPONSES["authentication_error"]
        )
        
        client = GrokClient(api_key="invalid_key")
        body = {"model": "x-ai/grok-4", "messages": []}
        
        with pytest.raises(AuthenticationError) as exc_info:
            client.post_openai_completion(
                messages=body["messages"],
                model=body["model"]
            )
        
        assert "Invalid API key" in str(exc_info.value)
    
    def test_streaming_request(self) -> None:
        """Test streaming response handling."""
        client = GrokClient(api_key=TEST_API_KEY)
        
        # Mock streaming response
        chunks = [
            b'data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n',
            b'data: {"choices":[{"delta":{"content":" world"}}]}\n\n',
            b'data: [DONE]\n\n'
        ]
        
        def mock_stream(*args, **kwargs):
            class MockResponse:
                def __enter__(self):
                    return self
                def __exit__(self, *args):
                    pass
                def iter_bytes(self):
                    for chunk in chunks:
                        yield chunk
                def raise_for_status(self):
                    pass
            return MockResponse()
        
        # Use httpx mock context
        with httpx.Client() as http_client:
            http_client.stream = mock_stream
            client._client = http_client
            
            body = {
                "model": "x-ai/grok-4",
                "messages": [{"role": "user", "content": "Hello"}],
                "stream": True
            }
            
            # Collect streamed chunks
            collected = []
            for chunk in client.stream_openai_completion(body):
                collected.append(chunk)
            
            # Should have received the chunks (minus [DONE])
            assert len(collected) == 2
            assert b"Hello" in collected[0]
            assert b"world" in collected[1]
    
    def test_post_anthropic_messages(self, httpx_mock: HTTPXMock) -> None:
        """Test Anthropic messages endpoint."""
        expected_response = create_messages_response("Test response")
        
        httpx_mock.add_response(
            method="POST",
            url=MESSAGES_URL,
            json=expected_response
        )
        
        client = GrokClient(api_key=TEST_API_KEY)
        body = {
            "model": "x-ai/grok-4",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 100
        }
        
        response = client.post_anthropic_messages(
            request_data={"messages": body["messages"]},
            model=body["model"],
            max_tokens=body.get("max_tokens")
        )
        
        assert response == expected_response
        
        # Verify request format
        request = httpx_mock.get_request()
        assert request is not None
        assert str(request.url) == MESSAGES_URL
        assert json.loads(request.content) == body
    
    def test_error_parsing(self) -> None:
        """Test error response parsing."""
        client = GrokClient(api_key=TEST_API_KEY)
        
        # Standard error format
        error_dict = {
            "error": {
                "message": "Test error message",
                "type": "invalid_request_error",
                "code": "test_error"
            }
        }
        
        parsed = client._parse_error_response(error_dict)
        assert parsed["message"] == "Test error message"
        assert parsed["type"] == "invalid_request_error"
        assert parsed["code"] == "test_error"
        
        # String error
        parsed = client._parse_error_response("Simple error message")
        assert parsed["message"] == "Simple error message"
        assert parsed["type"] == "unknown_error"